<!DOCTYPE html>
<html class="dark" lang="en">
<head>
    <meta charset="utf-8"/>
    <meta content="width=device-width, initial-scale=1.0" name="viewport"/>
    <title>Stitch Bible PWA - Complete Project</title>
    <link href="data:image/x-icon;base64," rel="icon" type="image/x-icon"/>
    <link href="https://fonts.googleapis.com" rel="preconnect"/>
    <link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect"/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:wght@400;500;700;800&display=swap" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com/css2?family=Manrope:wght@400;500;700;800&display=swap" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com/css2?family=Newsreader:ital,opsz,wght@0,6..72,200..800;1,6..72,200..800&display=swap" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined" rel="stylesheet"/>
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <script>
        tailwind.config = {
            darkMode: "class",
            theme: {
                extend: {
                    colors: {
                        primary: "#1193d4",
                        "background-light": "#f6f7f8",
                        "background-dark": "#101c22",
                    },
                    fontFamily: {
                        display: ["Inter", "Plus Jakarta Sans", "Manrope", "Newsreader"],
                    },
                    borderRadius: {
                        DEFAULT: "0.5rem",
                        lg: "1rem",
                        xl: "1.5rem",
                        full: "9999px",
                    },
                },
            },
        };
    </script>
    <style>
        :root {
            --primary-color: #1193d4;
            --background-light: #f6f7f8;
            --background-dark: #101c22;
            --text-light: #1F2937;
            --text-dark: #F9FAFB;
            --card-light: #FFFFFF;
            --card-dark: #1F2937;
        }
        .dark {
            --text-light: #F9FAFB;
            --text-dark: #1F2937;
            --card-light: #1F2937;
            --card-dark: #FFFFFF;
        }
        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--background-light);
            color: var(--text-light);
            -webkit-tap-highlight-color: transparent;
            min-height: 100vh;
        }
        .dark body {
            background-color: var(--background-dark);
            color: var(--text-dark);
        }
        .screen-container {
            min-height: 100vh;
            border-bottom: 2px solid var(--primary-color);
            margin-bottom: 2rem;
        }
        .verse-number {
            font-size: 0.7em;
            vertical-align: super;
            color: #94a3b8;
        }
        .dark .verse-number {
            color: #475569;
        }
        .material-symbols-outlined {
            font-variation-settings: "FILL" 0, 'wght' 400, 'GRAD' 0, 'opsz' 24;
        }
        .screen-title {
            position: sticky;
            top: 0;
            background: var(--primary-color);
            color: white;
            padding: 1rem;
            text-align: center;
            font-weight: bold;
            font-size: 1.25rem;
            z-index: 50;
        }
        .navigation-demo {
            position: fixed;
            top: 1rem;
            right: 1rem;
            background: var(--primary-color);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            font-size: 0.875rem;
            z-index: 100;
        }
    </style>
</head>
<body class="antialiased bg-background-light dark:bg-background-dark font-display">
    <div class="navigation-demo">
        📱 Stitch Bible PWA - Complete Project Demo
    </div>

    <!-- Screen 1: Welcome & Onboarding -->
    <div class="screen-container">
        <div class="screen-title">Welcome & Onboarding Screen</div>
        <div class="relative flex flex-col h-screen justify-between overflow-x-hidden p-6">
            <div class="flex flex-col items-center justify-center flex-grow text-center">
                <div class="bg-primary/20 dark:bg-primary/30 text-primary p-5 rounded-xl inline-block shadow-lg mb-8">
                    <svg class="w-16 h-16" fill="none" stroke="currentColor" stroke-width="1.5" viewBox="0 0 24 24">
                        <path d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25" stroke-linecap="round" stroke-linejoin="round"></path>
                    </svg>
                </div>
                <h1 class="text-4xl font-bold text-gray-900 dark:text-white">Your Daily Bread</h1>
                <p class="text-lg text-gray-600 dark:text-gray-300 mt-2">Find clarity and peace in scripture.</p>
            </div>
            <div class="flex-shrink-0">
                <div class="flex justify-center items-center space-x-6 text-gray-500 dark:text-gray-400 mb-8">
                    <div class="flex flex-col items-center space-y-1">
                        <span class="material-symbols-outlined text-2xl">edit_note</span>
                        <span class="text-xs font-medium">Notes</span>
                    </div>
                    <div class="flex flex-col items-center space-y-1">
                        <span class="material-symbols-outlined text-2xl">event</span>
                        <span class="text-xs font-medium">Plans</span>
                    </div>
                    <div class="flex flex-col items-center space-y-1">
                        <span class="material-symbols-outlined text-2xl">headphones</span>
                        <span class="text-xs font-medium">Audio</span>
                    </div>
                    <div class="flex flex-col items-center space-y-1">
                        <span class="material-symbols-outlined text-2xl">cloud_off</span>
                        <span class="text-xs font-medium">Offline</span>
                    </div>
                </div>
                <div class="px-6 py-6">
                    <button class="w-full bg-primary text-white font-bold py-4 px-5 rounded-xl shadow-lg shadow-primary/30 hover:bg-primary/90 transition-all duration-300 transform hover:scale-105">
                        Get Started
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Screen 2: Home Dashboard -->
    <div class="screen-container">
        <div class="screen-title">Home Dashboard</div>
        <div class="relative flex h-auto min-h-screen w-full flex-col justify-between overflow-x-hidden">
            <div class="flex-grow">
                <header class="sticky top-0 z-10 bg-background-light/80 dark:bg-background-dark/80 backdrop-blur-sm">
                    <div class="flex items-center p-4 pb-2 justify-between">
                        <div class="w-12"></div>
                        <h1 class="flex-1 text-center text-lg font-bold text-gray-900 dark:text-white">Home</h1>
                        <div class="flex w-12 items-center justify-end">
                            <button class="flex h-12 w-12 items-center justify-center rounded-full text-gray-600 dark:text-gray-300 hover:bg-primary/10 dark:hover:bg-primary/20">
                                <span class="material-symbols-outlined">settings</span>
                            </button>
                        </div>
                    </div>
                </header>
                <main class="p-4">
                    <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">Welcome back, Ethan</h2>
                    <section class="mb-8">
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-4">Daily Reading</h3>
                        <div class="flex items-stretch justify-between gap-4 rounded-lg bg-white/5 dark:bg-white/5 p-4 shadow-sm">
                            <div class="flex flex-col gap-1 flex-1">
                                <p class="text-sm text-gray-500 dark:text-gray-400">Psalm 23</p>
                                <p class="text-base font-bold text-gray-900 dark:text-white">The Lord is my shepherd...</p>
                                <p class="text-sm text-gray-500 dark:text-gray-400">A psalm of David.</p>
                            </div>
                            <div class="w-24 h-24 bg-primary/20 rounded-lg flex items-center justify-center">
                                <span class="material-symbols-outlined text-primary text-3xl">auto_stories</span>
                            </div>
                        </div>
                    </section>
                </main>
            </div>
        </div>
    </div>

    <!-- Screen 3: Bible Reader -->
    <div class="screen-container">
        <div class="screen-title">Bible Reader - Genesis 1</div>
        <div class="flex flex-col min-h-screen">
            <header class="sticky top-0 bg-background-light/80 dark:bg-background-dark/80 backdrop-blur-sm z-10 border-b border-slate-200/50 dark:border-slate-800/50">
                <div class="container mx-auto px-4">
                    <div class="flex items-center justify-between h-16">
                        <button class="p-2 rounded-full hover:bg-slate-200/50 dark:hover:bg-slate-800/50">
                            <span class="material-symbols-outlined text-slate-600 dark:text-slate-400">menu</span>
                        </button>
                        <h1 class="text-lg font-bold">Genesis 1</h1>
                        <button class="p-2 rounded-full hover:bg-slate-200/50 dark:hover:bg-slate-800/50">
                            <span class="material-symbols-outlined text-slate-600 dark:text-slate-400">play_arrow</span>
                        </button>
                    </div>
                </div>
            </header>
            <main class="flex-grow container mx-auto px-6 py-8">
                <div class="text-lg leading-relaxed space-y-4">
                    <p>
                        <span class="verse-number">1</span>In the beginning God created the heavens and the earth.
                        <span class="verse-number">2</span>Now the earth was formless and empty, darkness was over the surface of the deep, and the Spirit of God was hovering over the waters.
                    </p>
                    <p>
                        <span class="verse-number">3</span>And God said, "Let there be light," and there was light.
                        <span class="verse-number">4</span>God saw that the light was good, and he separated the light from the darkness.
                        <span class="verse-number">5</span>God called the light "day," and the darkness he called "night." And there was evening, and there was morning—the first day.
                    </p>
                    <p>
                        <span class="verse-number">6</span>And God said, "Let there be a vault between the waters to separate water from water."
                        <span class="verse-number">7</span>So God made the vault and separated the water under the vault from the water above it. And it was so.
                        <span class="verse-number">8</span>God called the vault "sky." And there was evening, and there was morning—the second day.
                    </p>
                </div>
            </main>
        </div>
    </div>

    <!-- Screen 4: Notes List -->
    <div class="screen-container">
        <div class="screen-title">Notes List</div>
        <div class="relative flex h-screen w-full flex-col">
            <header class="sticky top-0 z-10 bg-background-light/80 dark:bg-background-dark/80 backdrop-blur-sm">
                <div class="flex items-center justify-between px-4 pt-4 pb-2">
                    <div class="w-12"></div>
                    <h1 class="flex-1 text-center text-xl font-bold text-slate-900 dark:text-white">Notes</h1>
                    <div class="w-12"></div>
                </div>
                <div class="px-4 pb-4">
                    <div class="relative">
                        <span class="material-symbols-outlined pointer-events-none absolute left-3 top-1/2 -translate-y-1/2 text-slate-400 dark:text-slate-500">search</span>
                        <input class="w-full rounded-full border-transparent bg-slate-200/60 dark:bg-slate-800/60 py-3 pl-10 pr-4 text-slate-900 placeholder:text-slate-400 dark:text-white dark:placeholder:text-slate-500 focus:border-primary focus:ring-primary" placeholder="Search notes" type="search"/>
                    </div>
                </div>
            </header>
            <main class="flex-1 overflow-y-auto">
                <div class="space-y-8 p-4">
                    <div>
                        <h2 class="mb-3 text-lg font-bold text-slate-900 dark:text-white">Genesis</h2>
                        <div class="space-y-4">
                            <div>
                                <h3 class="mb-2 pl-4 text-sm font-semibold text-slate-500 dark:text-slate-400">Chapter 1</h3>
                                <div class="space-y-1">
                                    <div class="group relative rounded-lg bg-slate-100 dark:bg-slate-800/60">
                                        <div class="p-4">
                                            <p class="mb-1 font-bold text-slate-600 dark:text-slate-300">Genesis 1:1</p>
                                            <p class="text-slate-800 dark:text-slate-200">This is a profound statement about the origins of everything. It's the foundational verse for understanding God's creative power.</p>
                                        </div>
                                        <div class="absolute right-2 top-2">
                                            <button class="flex h-8 w-8 items-center justify-center rounded-full text-slate-500 hover:bg-slate-200 dark:text-slate-400 dark:hover:bg-slate-700">
                                                <span class="material-symbols-outlined text-xl">more_vert</span>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
            <button class="absolute bottom-24 right-4 z-20 flex h-14 w-14 items-center justify-center rounded-full bg-primary text-white shadow-lg transition-transform hover:scale-105 active:scale-95">
                <span class="material-symbols-outlined text-3xl">add</span>
            </button>
        </div>
    </div>

    <!-- Screen 5: Spurgeon Resources -->
    <div class="screen-container">
        <div class="screen-title">Spurgeon Library</div>
        <div class="flex flex-col h-screen justify-between">
            <div class="flex-grow overflow-y-auto">
                <header class="sticky top-0 bg-background-light/80 dark:bg-background-dark/80 backdrop-blur-sm z-10">
                    <div class="flex items-center p-4">
                        <button class="p-2 -ml-2">
                            <span class="material-symbols-outlined text-gray-800 dark:text-gray-200">arrow_back_ios_new</span>
                        </button>
                        <h1 class="text-xl font-bold text-center flex-1 text-gray-900 dark:text-white pr-8">Spurgeon</h1>
                    </div>
                    <nav class="border-b border-gray-200/20 dark:border-gray-700/50 px-4">
                        <div class="flex items-center justify-start space-x-8">
                            <a class="py-3 text-sm font-semibold border-b-2 border-primary text-primary" href="#">Sermons</a>
                            <a class="py-3 text-sm font-semibold border-b-2 border-transparent text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary transition-colors" href="#">Notes</a>
                            <a class="py-3 text-sm font-semibold border-b-2 border-transparent text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary transition-colors" href="#">Audio</a>
                        </div>
                    </nav>
                </header>
                <main class="p-4 space-y-4">
                    <div class="relative">
                        <span class="material-symbols-outlined absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 dark:text-gray-500">search</span>
                        <input class="w-full pl-10 pr-4 py-3 rounded-lg bg-gray-200/50 dark:bg-gray-800/50 border-none focus:ring-2 focus:ring-primary focus:outline-none placeholder-gray-500 dark:placeholder-gray-400" placeholder="Search sermons" type="search"/>
                    </div>
                    <ul class="divide-y divide-gray-200/20 dark:divide-gray-700/50">
                        <li class="flex items-center gap-4 py-4">
                            <div class="flex items-center justify-center rounded-lg bg-primary/10 shrink-0 size-12 text-primary">
                                <span class="material-symbols-outlined">description</span>
                            </div>
                            <div class="flex-grow">
                                <p class="font-semibold text-gray-900 dark:text-white">The Genealogy of Jesus Christ</p>
                                <p class="text-sm text-gray-500 dark:text-gray-400">Matthew 1:1-17</p>
                            </div>
                            <button class="p-2 -mr-2 text-gray-400 dark:text-gray-500">
                                <span class="material-symbols-outlined">more_vert</span>
                            </button>
                        </li>
                        <li class="flex items-center gap-4 py-4">
                            <div class="flex items-center justify-center rounded-lg bg-primary/10 shrink-0 size-12 text-primary">
                                <span class="material-symbols-outlined">description</span>
                            </div>
                            <div class="flex-grow">
                                <p class="font-semibold text-gray-900 dark:text-white">The Birth of Jesus Christ</p>
                                <p class="text-sm text-gray-500 dark:text-gray-400">Matthew 1:18-25</p>
                            </div>
                            <button class="p-2 -mr-2 text-gray-400 dark:text-gray-500">
                                <span class="material-symbols-outlined">more_vert</span>
                            </button>
                        </li>
                    </ul>
                </main>
            </div>
        </div>
    </div>

    <!-- Screen 6: Offline Download Manager -->
    <div class="screen-container">
        <div class="screen-title">Offline Download Manager</div>
        <div class="relative flex h-screen min-h-screen w-full flex-col group/design-root overflow-x-hidden">
            <header class="sticky top-0 z-10 flex h-16 items-center justify-between border-b border-primary/20 bg-background-light px-4 dark:bg-background-dark dark:border-primary/30">
                <button class="flex h-10 w-10 items-center justify-center rounded-full text-zinc-900 dark:text-white">
                    <span class="material-symbols-outlined">arrow_back</span>
                </button>
                <h1 class="text-lg font-bold text-zinc-900 dark:text-white">Downloads</h1>
                <div class="h-10 w-10"></div>
            </header>
            <main class="flex-1 overflow-y-auto p-4">
                <section class="space-y-4">
                    <h2 class="text-xl font-bold text-zinc-900 dark:text-white">Translations</h2>
                    <div class="space-y-2">
                        <div class="flex items-center gap-4 rounded-lg bg-primary/10 p-4 dark:bg-primary/20">
                            <div class="flex h-12 w-12 shrink-0 items-center justify-center rounded-full bg-primary/20 text-primary dark:bg-primary/30">
                                <span class="material-symbols-outlined">menu_book</span>
                            </div>
                            <div class="flex-1">
                                <div class="flex justify-between">
                                    <p class="font-bold text-zinc-900 dark:text-white">ESV</p>
                                    <p class="text-sm font-medium text-zinc-900 dark:text-white">75%</p>
                                </div>
                                <p class="text-sm text-zinc-600 dark:text-zinc-400">English Standard Version</p>
                                <div class="mt-2 h-1.5 w-full overflow-hidden rounded-full bg-primary/20 dark:bg-primary/30">
                                    <div class="h-full rounded-full bg-primary" style="width: 75%;"></div>
                                </div>
                            </div>
                        </div>
                        <div class="flex items-center gap-4 rounded-lg bg-primary/10 p-4 dark:bg-primary/20">
                            <div class="flex h-12 w-12 shrink-0 items-center justify-center rounded-full bg-primary/20 text-primary dark:bg-primary/30">
                                <span class="material-symbols-outlined">menu_book</span>
                            </div>
                            <div class="flex-1">
                                <div class="flex justify-between">
                                    <p class="font-bold text-zinc-900 dark:text-white">NIV</p>
                                    <p class="text-sm font-medium text-zinc-500 dark:text-zinc-400">Downloaded</p>
                                </div>
                                <p class="text-sm text-zinc-600 dark:text-zinc-400">New International Version</p>
                            </div>
                            <button class="h-10 w-10 shrink-0 text-zinc-600 dark:text-zinc-400">
                                <span class="material-symbols-outlined">check_circle</span>
                            </button>
                        </div>
                    </div>
                </section>
            </main>
        </div>
    </div>

    <!-- Navigation Footer Demo -->
    <div class="screen-container">
        <div class="screen-title">Navigation Footer Example</div>
        <div class="flex flex-col min-h-screen">
            <main class="flex-grow p-8 text-center">
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">Navigation Demo</h2>
                <p class="text-gray-600 dark:text-gray-300 mb-8">This shows the consistent navigation pattern used throughout the app.</p>
                <div class="bg-primary/10 dark:bg-primary/20 rounded-lg p-6">
                    <p class="text-primary font-semibold">All screens feature a consistent bottom navigation with:</p>
                    <ul class="mt-4 space-y-2 text-left max-w-md mx-auto">
                        <li class="flex items-center gap-2">
                            <span class="material-symbols-outlined text-primary">home</span>
                            <span>Home Dashboard</span>
                        </li>
                        <li class="flex items-center gap-2">
                            <span class="material-symbols-outlined text-primary">menu_book</span>
                            <span>Bible Reader</span>
                        </li>
                        <li class="flex items-center gap-2">
                            <span class="material-symbols-outlined text-primary">edit_note</span>
                            <span>Notes & Study</span>
                        </li>
                        <li class="flex items-center gap-2">
                            <span class="material-symbols-outlined text-primary">bookmark</span>
                            <span>Library & Resources</span>
                        </li>
                        <li class="flex items-center gap-2">
                            <span class="material-symbols-outlined text-primary">search</span>
                            <span>Search</span>
                        </li>
                    </ul>
                </div>
            </main>
            <footer class="sticky bottom-0 bg-background-light/90 dark:bg-background-dark/90 backdrop-blur-sm border-t border-gray-200/20 dark:border-gray-700/50 pb-safe">
                <nav class="flex justify-around items-center px-4 pt-2 pb-3">
                    <a class="flex flex-col items-center justify-end gap-1 text-primary" href="#">
                        <span class="material-symbols-outlined">home</span>
                        <span class="text-xs font-medium">Home</span>
                    </a>
                    <a class="flex flex-col items-center justify-end gap-1 text-gray-500 dark:text-gray-400 hover:text-primary transition-colors" href="#">
                        <span class="material-symbols-outlined">menu_book</span>
                        <span class="text-xs font-medium">Bible</span>
                    </a>
                    <a class="flex flex-col items-center justify-end gap-1 text-gray-500 dark:text-gray-400 hover:text-primary transition-colors" href="#">
                        <span class="material-symbols-outlined">edit_note</span>
                        <span class="text-xs font-medium">Notes</span>
                    </a>
                    <a class="flex flex-col items-center justify-end gap-1 text-gray-500 dark:text-gray-400 hover:text-primary transition-colors" href="#">
                        <span class="material-symbols-outlined">bookmark</span>
                        <span class="text-xs font-medium">Library</span>
                    </a>
                    <a class="flex flex-col items-center justify-end gap-1 text-gray-500 dark:text-gray-400 hover:text-primary transition-colors" href="#">
                        <span class="material-symbols-outlined">search</span>
                        <span class="text-xs font-medium">Search</span>
                    </a>
                </nav>
            </footer>
        </div>
    </div>

    <!-- Project Summary -->
    <div class="screen-container">
        <div class="screen-title">Project Summary</div>
        <div class="p-8 max-w-4xl mx-auto">
            <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-6">Stitch Bible PWA Project</h2>
            <div class="grid md:grid-cols-2 gap-6">
                <div class="bg-white/5 dark:bg-white/5 rounded-lg p-6">
                    <h3 class="text-xl font-bold text-primary mb-4">Features Included</h3>
                    <ul class="space-y-2 text-gray-700 dark:text-gray-300">
                        <li>✅ Welcome & Onboarding Flow</li>
                        <li>✅ Home Dashboard with Daily Reading</li>
                        <li>✅ Bible Reader with Verse Numbers</li>
                        <li>✅ Notes Management System</li>
                        <li>✅ Spurgeon Resources Library</li>
                        <li>✅ Offline Download Manager</li>
                        <li>✅ Dark/Light Mode Support</li>
                        <li>✅ Responsive Mobile Design</li>
                    </ul>
                </div>
                <div class="bg-white/5 dark:bg-white/5 rounded-lg p-6">
                    <h3 class="text-xl font-bold text-primary mb-4">Technical Stack</h3>
                    <ul class="space-y-2 text-gray-700 dark:text-gray-300">
                        <li>🎨 Tailwind CSS for styling</li>
                        <li>🔤 Multiple Google Fonts</li>
                        <li>🎯 Material Symbols Icons</li>
                        <li>📱 Mobile-first responsive design</li>
                        <li>🌙 CSS custom properties for theming</li>
                        <li>⚡ Progressive Web App ready</li>
                        <li>🎭 Smooth animations & transitions</li>
                        <li>♿ Accessibility considerations</li>
                    </ul>
                </div>
            </div>
            <div class="mt-8 bg-primary/10 dark:bg-primary/20 rounded-lg p-6 text-center">
                <h3 class="text-xl font-bold text-primary mb-2">Ready for AI Studio</h3>
                <p class="text-gray-700 dark:text-gray-300">This complete HTML file contains all screens and components from the Stitch Bible PWA project, ready for upload to aistudio.google.com for analysis and further development.</p>
            </div>
        </div>
    </div>

    <script>
        // Dark mode toggle functionality
        if (localStorage.getItem('color-theme') === 'dark' || (!('color-theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            document.documentElement.classList.add('dark');
        } else {
            document.documentElement.classList.remove('dark')
        }

        // Add smooth scrolling between sections
        document.addEventListener('DOMContentLoaded', function() {
            const screens = document.querySelectorAll('.screen-container');
            let currentScreen = 0;

            function scrollToScreen(index) {
                if (index >= 0 && index < screens.length) {
                    screens[index].scrollIntoView({ behavior: 'smooth' });
                    currentScreen = index;
                }
            }

            // Keyboard navigation
            document.addEventListener('keydown', function(e) {
                if (e.key === 'ArrowDown' || e.key === 'PageDown') {
                    e.preventDefault();
                    scrollToScreen(currentScreen + 1);
                } else if (e.key === 'ArrowUp' || e.key === 'PageUp') {
                    e.preventDefault();
                    scrollToScreen(currentScreen - 1);
                }
            });
        });
    </script>
</body>
</html>
